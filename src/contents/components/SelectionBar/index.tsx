import React, { useState, useEffect } from 'react';
import AIProcessModal from '../AIProcessModal';
import { getMainButtons, getDropdownItems, isAIAction } from '../../../config/menuItems';
import type { MenuItemType } from '../../../config/menuItems';
import * as styles from "./index.module.less";

// 导入测试工具（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  import('../../utils/testAIService');
}

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
  onAIProcessModalVisibilityChange?: (visible: boolean) => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose, onAIProcessModalVisibilityChange }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [currentAction, setCurrentAction] = useState('');
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });

  console.log('SelectionBar render: showDropdown =', showDropdown, 'showModal =', showModal);

  useEffect(() => {
    console.log('SelectionBar: showDropdown state changed to:', showDropdown);
  }, [showDropdown]);

  const handleAction = (action: string) => {
    // AI处理类操作显示弹窗
    if (isAIAction(action)) {
      setCurrentAction(action);
      setShowModal(true);
      setShowDropdown(false);

      // 通知父组件AIProcessModal即将显示
      onAIProcessModalVisibilityChange?.(true);

      // 计算弹窗位置（使用与原来工具栏相同的定位逻辑）
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();

        // 使用与 WebAssistantManager.positionSelectionBar 相同的逻辑
        const offsetY = 8;
        const modalWidth = 480; // AI弹窗的实际宽度（与CSS中的width保持一致）
        const modalHeight = 400; // AI弹窗的估算高度

        let left = rect.left;
        let top = rect.bottom + offsetY;

        // 确保弹窗不会超出视窗右边界
        const viewportWidth = window.innerWidth;
        if (left + modalWidth > viewportWidth) {
          left = rect.right - modalWidth;
        }

        // 确保弹窗不会超出视窗下边界
        const viewportHeight = window.innerHeight;
        if (top + modalHeight > viewportHeight) {
          top = rect.top - modalHeight - offsetY;
        }

        // 确保不会超出左边界和上边界
        left = Math.max(5, left);
        top = Math.max(5, top);

        setModalPosition({
          x: left,
          y: top
        });
      }
    } else {
      // 其他操作保持原有逻辑
      onAction(action);
      if (action !== 'open-panel') {
        onClose();
      }
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    e.nativeEvent.stopImmediatePropagation(); // 阻止原生事件的立即传播
    console.log('Toggling dropdown, current state:', showDropdown, 'will become:', !showDropdown);
    setShowDropdown(!showDropdown);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setCurrentAction('');

    // 通知父组件AIProcessModal已关闭
    onAIProcessModalVisibilityChange?.(false);

    onClose(); // 同时关闭SelectionBar
  };

  // 渲染主要按钮
  const renderMainButton = (item: MenuItemType) => {
    const commonProps = {
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
        handleAction(item.action);
      },
      onMouseDown: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
      }
    };

    if (item.type === 'icon') {
      return (
        <div
          key={item.id}
          className={styles.selectionIcon}
          title={item.title}
          {...commonProps}
        >
          {/* 图标内容可以在这里添加 */}
        </div>
      );
    }

    if (item.type === 'button') {
      return (
        <div
          key={item.id}
          className={styles.selectionButton}
          {...commonProps}
        >
          <span>{item.label}</span>
        </div>
      );
    }

    return null;
  };

  return (
    <>
      <div className={styles.selectionContainer}>
        {/* 箭头指向选中文本 */}
        <div className={styles.selectionArrow} />

        <div className={styles.selectionBar}>
          {/* 渲染主要按钮 */}
          {getMainButtons().map(renderMainButton)}

      <div className={styles.selectionMore}>
        <div
          className={styles.selectionDots}
          onClick={toggleDropdown}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onMouseUp={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          ⋮
        </div>
        {showDropdown && (
          <div
            className={styles.selectionDropdown}
            onClick={(e) => e.stopPropagation()}
          >
            {getDropdownItems().map((item) => (
              <div
                key={item.id}
                className={styles.selectionDropdownItem}
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction(item.action);
                }}
              >
                {item.label}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 分隔线 */}
      <div className={styles.selectionDivider} />

      <div
        className={styles.selectionClose}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onClose();
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        title="关闭"
      >
        ×
        </div>
        </div>
      </div>

      {/* AI处理弹窗 */}
      <AIProcessModal
        isVisible={showModal}
        selectedText={selectedText}
        actionType={currentAction}
        onClose={handleModalClose}
        position={modalPosition}
      />
    </>
  );
};

export default SelectionBar;
